<template>
    <div>
        <div class="flex-between gap-2">
            <AppText look="heading3" tag="h2"> {{ $t("home.breaking.title") }} </AppText>
            <NuxtLink :to="localePath({ name: routesName.announcements })">
                <NButton icon-placement="right" class="rounded-lg">
                    <span>{{ $t("home.breaking.more") }}</span>
                    <template #icon>
                        <span class="i-solar:arrow-right-linear"></span>
                    </template>
                </NButton>
            </NuxtLink>
        </div>
        <NTabs v-model:value="currentTab" :bar-width="30" type="line" default-value="announcements" class="my-5">
            <NTab v-for="tab in tabs" :key="tab.name" :name="tab.name" :tab="tab.tab">
                {{ tab.tab }}
            </NTab>
        </NTabs>
        <div class="space-y-4 overflow-auto max-h-110">
            <template v-if="dataBreaking && dataBreaking.length > 0" v-for="item in dataBreaking" :key="item.id">
                <NuxtLink :to="getLink(item)" class="block group hover:text-primary">
                    <AppText look="body2Regular" tag="div" class="space-y-1">
                        <p>{{ item.title }}</p>
                        <p class="text-text-secondary">
                            {{ formatRelativeTime(item.date_created ?? "") }}
                        </p>
                    </AppText>
                </NuxtLink>
            </template>
        </div>
    </div>
</template>

<script setup lang="ts">
import { routesName } from "~/config/page";
import type { Announcement, AnnouncementsTranslation, Blog, BlogTranslation } from "#shared/types/directus-types";

const { localeProperties, t } = useI18n();
const localePath = useLocalePath();
const { formatters } = useDateFormat();

const tabs = [
    { tab: t("home.breaking.announcements"), name: "announcements" },
    { tab: t("blog.title"), name: "blog" },
];
const currentTab = ref("announcements");

const { data } = await useAsyncData("breaking", () =>
    Promise.all([
        $fetch<(Announcement & AnnouncementsTranslation)[]>(
            `/api/content/announcements/${localeProperties.value.name}`,
            {
                query: {
                    limit: 10,
                    page: 1,
                    fields: ["id", "date_created", "translations.slug", "translations.title"],
                    sort: ["-date_created"],
                },
            },
        ),
        $fetch<(Blog & BlogTranslation)[]>(`/api/content/blog/${localeProperties.value.name}`, {
            query: {
                limit: 10,
                page: 1,
                fields: ["id", "date_created", "translations.slug", "translations.title"],
                sort: ["-date_created"],
            },
        }),
    ]),
    {
        server: true,
        default: () => [[], []]
    }
);

const dataBreaking = computed(() => {
    const [announcements, blog] = data.value ?? [];
    return currentTab.value === "announcements" ? announcements : blog;
});

const getLink = (item: (Announcement & AnnouncementsTranslation) | (Blog & BlogTranslation)) => {
    if (currentTab.value === "announcements") {
        return localePath({ name: routesName.announcementsDetail, params: { slug: item.slug } });
    } else {
        return localePath({ name: routesName.blogDetail, params: { slug: item.slug } });
    }
};

const formatRelativeTime = (dateString: string): string => {
    if (!dateString) return "";

    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));

    if (diffInMinutes < 60) {
        return `${diffInMinutes} ${t("home.breaking.minutes")}`;
    } else if (diffInMinutes < 1440) {
        const hours = Math.floor(diffInMinutes / 60);
        return `${hours} ${t("home.breaking.hours")}`;
    } else {
        // Use locale-aware date formatting to prevent hydration mismatch
        return formatters.short(date);
    }
};
</script>
