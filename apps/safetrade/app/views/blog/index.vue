<template>
    <div class="container py-8">
        <div class="gap-4 mb-8">
            <AppText look="heading1" tag="h1" class="mb-2">{{ $t("blog.title") }}</AppText>
            <AppText look="body2Regular" tag="p" class="text-text-secondary">
                {{ $t("blog.description") }}
            </AppText>
        </div>

        <NTabs :bar-width="30" type="line" v-model:value="currentCategory" :default-value="CATEGORY_ALL" class="mb-8">
            <NTab v-for="tab in blogCategories" :key="tab.id" :name="tab.id">
                {{ tab.title }}
            </NTab>
        </NTabs>

        <div class="grid lg:grid-cols-10 gap-8">
            <div class="lg:col-span-7 space-y-6">
                <CardArticle v-for="article in transformedBlogData" :key="article.id" v-bind="article" />
                <div v-if="shouldShowPagination" class="flex-center pb-10">
                    <NPagination :page-count="totalPageCount" :page="currentPage" @update:page="handlePageChange" />
                </div>
            </div>
            <div class="lg:col-span-3 space-y-6">
                <Sidebar class="h-max sticky top-20" />
            </div>
        </div>
    </div>
    <AppSeo collection="blog_index" />
</template>

<script setup lang="ts">
import CardArticle from "./components/CardArticle.vue";
import Sidebar from "./components/Sidebar.vue";
import type { BlogTranslation } from "~~/shared/types/directus-types";

type BlogCategory = {
    id: number;
    title: string;
};

type TransformedBlogItem = BlogTranslation & {
    image: string;
    date: string;
    categories: string[];
};

const { localeProperties, t } = useI18n();
const { formatters } = useDateFormat();
const { formatters } = useDateFormat();

const CATEGORY_ALL = 99;
const ITEMS_PER_PAGE = 12;

const currentCategory = ref<number>(CATEGORY_ALL);
const currentPage = ref<number>(1);

const blogApiKey = computed(() => `data-blog-${currentCategory.value}-${currentPage.value}`);
const totalApiKey = computed(() => `data-total-blog-${currentCategory.value}`);
const locale = computed(() => localeProperties.value.name);

const blogFilter = computed(() => ({
    ...(currentCategory.value !== CATEGORY_ALL && {
        categories: {
            blog_categories_id: {
                id: { _eq: currentCategory.value },
            },
        },
    }),
}));

const { data: blogCategories } = await useFetch<BlogCategory[]>(`/api/content/blog_categories/${locale.value}`, {
    params: {
        fields: ["id", "translations.*"],
    },
    transform: (data: any[]): BlogCategory[] => {
        const allItem: BlogCategory = { id: CATEGORY_ALL, title: t("blog.all") };
        return [allItem, ...data];
    },
});

const { data: dataBlog } = await useAsyncData(
    blogApiKey,
    async (): Promise<BlogTranslation[]> =>
        $fetch<BlogTranslation[]>(`/api/content/blog/${locale.value}`, {
            query: {
                limit: ITEMS_PER_PAGE,
                page: currentPage.value,
                fields: [
                    "id",
                    "image",
                    "date_created",
                    "translations.*",
                    "categories.blog_categories_id.translations.*",
                ],
                filter: blogFilter.value,
            },
        }),
    {
        watch: [currentCategory, currentPage],
    },
);

const { data: totalPages } = useLazyAsyncData(
    totalApiKey,
    (): Promise<number> =>
        $fetch<number>(`/api/total/blog`, {
            query: {
                filter: blogFilter.value,
            },
        }),
    {
        watch: [currentCategory],
        default: (): number => 0,
    },
);

const transformedBlogData = computed((): TransformedBlogItem[] => {
    if (!dataBlog.value) return [];

    return dataBlog.value.map(
        (item: any): TransformedBlogItem => ({
            ...item,
            date: formatters.short(item.date_created),
            image: item.image,
            categories: extractCategoryTitles(item.categories),
        }),
    );
});

const totalPageCount = computed((): number => Math.ceil(Number(totalPages.value) / ITEMS_PER_PAGE));

const shouldShowPagination = computed((): boolean => totalPageCount.value > 1);

const extractCategoryTitles = (categories: any[]): string[] => {
    if (!categories?.length) return [];

    return categories
        .map(
            (category: any) =>
                category.blog_categories_id?.translations?.find(
                    (translation: any) => translation.languages_code === locale.value,
                )?.title,
        )
        .filter(Boolean);
};

const handlePageChange = (page: number): void => {
    currentPage.value = page;
};

watch(currentCategory, () => {
    currentPage.value = 1;
});

useSeoMeta({
    title: t("blog.title"),
    description: t("blog.description"),
});
</script>
