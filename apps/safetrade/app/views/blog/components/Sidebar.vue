<template>
    <div class="space-y-10">
        <div class="flex-items gap-3">
            <AppText look="body2Regular" tag="span" class="text-text-secondary">
                {{ isDetail ? $t("blog.shareThisArticle") : $t("blog.followUs") }}:
            </AppText>

            <ul class="flex-items gap-2 flex-wrap">
                <li v-for="item in networksShareLink" :key="item.icon" class="size-6">
                    <NuxtLink :to="item.shareUrl || item.url" target="_blank" external>
                        <span :class="item.icon" class="size-full rounded-full hover:opacity-80 duration-200"></span>
                    </NuxtLink>
                </li>
            </ul>
        </div>
        <div>
            <div class="flex-items gap-2 mb-4">
                <AppText look="heading5" tag="h3">{{ $t("blog.hotTopics") }}</AppText>
            </div>
            <div class="space-y-2">
                <NuxtLink
                    v-for="topic in hotTopics"
                    :key="topic.id"
                    :to="localePath({ name: 'blog-detail', params: { slug: topic.slug } })"
                    class="flex-items gap-3 p-2 rounded-md hover:bg-bg-2 cursor-pointer duration-200"
                >
                    <div class="flex-1">
                        <AppText look="body2Medium" tag="p" class="hover:text-primary duration-200">
                            {{ topic.title }}
                        </AppText>
                        <AppText look="body1Regular" tag="span" class="text-text-secondary">
                            {{ topic.date }}
                        </AppText>
                    </div>
                </NuxtLink>
            </div>
        </div>

        <div class="bg-gradient-to-br from-teal to-primary rounded-2xl p-6 text-white">
            <div class="text-center space-y-4">
                <AppText look="heading5" tag="h3">{{ $t("blog.telegram.title") }}</AppText>
                <AppText look="body2Regular" tag="p" class="opacity-90">
                    {{ $t("blog.telegram.description") }}
                </AppText>
                <NButton class="w-full rounded-lg">
                    {{ $t("blog.telegram.joinNow") }}
                </NButton>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { BlogTranslation, Blog } from "~~/shared/types/directus-types";
type MergeBlog = BlogTranslation & Blog;

const props = defineProps<{
    isDetail?: boolean;
    title?: string;
    slug?: string;
}>();

const { localeProperties } = useI18n();
const localePath = useLocalePath();
const { formatters } = useDateFormat();

const { data: hotTopics } = await useLazyAsyncData(
    `data-blog-sidebar`,
    async () =>
        $fetch<MergeBlog[]>(`/api/content/blog/${localeProperties.value.name}`, {
            query: {
                limit: 4,
                page: 1,
                fields: ["id", "image", "translations.*"],
                filter: {
                    is_featured: { _eq: true },
                },
            },
        }),
    {
        transform: (data: MergeBlog[]) => {
            return data.map((item) => ({
                ...item,
                date: formatters.short(item.date_created || new Date()),
            }));
        },
    },
);

const networks = useNetworks();

type SocialNetwork = { icon: string; url: string; shareUrl?: string };
const getShareUrl = (network: string, url: string, title: string): string => {
    switch (network) {
        case "x":
            return `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`;
        case "facebook":
            return `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        case "medium":
            return `https://medium.com/p/import?url=${encodeURIComponent(url)}`;
        case "redis":
            return `https://www.reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
        default:
            return url;
    }
};

const networksShareLink = computed<SocialNetwork[]>(() => {
    const SOCIAL_SHARE_LINK = ["x", "facebook", "medium", "redis"];

    if (!import.meta.client) return networks.value;

    const url = window.location.href;
    const title = "SafeTrade | " + props.title;

    if (props.isDetail) {
        return networks.value
            .filter((network) => SOCIAL_SHARE_LINK.includes(network.icon.split("-").pop()?.toLowerCase() || ""))
            .map((network) => {
                const networkKey = network.icon.split("-").pop()?.toLowerCase() || "";
                return {
                    ...network,
                    shareUrl: getShareUrl(networkKey, url, title),
                };
            });
    }
    return networks.value;
});
</script>
