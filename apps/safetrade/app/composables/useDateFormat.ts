/**
 * Locale-aware date formatting composable to prevent hydration mismatches
 * This ensures consistent date formatting between server and client
 */
export const useDateFormat = () => {
    const { localeProperties } = useI18n();

    /**
     * Format date with locale awareness
     * @param date - Date to format
     * @param options - Intl.DateTimeFormatOptions
     * @returns Formatted date string
     */
    const formatDate = (date: Date | string, options: Intl.DateTimeFormatOptions = {}) => {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        
        if (!dateObj || isNaN(dateObj.getTime())) {
            return '';
        }

        return dateObj.toLocaleDateString(localeProperties.value.name, options);
    };

    /**
     * Format date with common patterns
     */
    const formatters = {
        short: (date: Date | string) => formatDate(date, {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }),
        
        long: (date: Date | string) => formatDate(date, {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }),
        
        dateTime: (date: Date | string) => formatDate(date, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        }),
        
        time: (date: Date | string) => formatDate(date, {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        })
    };

    return {
        formatDate,
        formatters
    };
};
